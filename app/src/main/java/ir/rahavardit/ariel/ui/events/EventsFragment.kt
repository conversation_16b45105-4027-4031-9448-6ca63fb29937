package ir.rahavardit.ariel.ui.events

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.databinding.FragmentEventsBinding
import ir.rahavardit.ariel.utils.FragmentUtils
import ir.rahavardit.ariel.utils.setupPagination

/**
 * Fragment for displaying a list of event items.
 */
class EventsFragment : Fragment() {

    private var _binding: FragmentEventsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: EventsViewModel
    private lateinit var eventAdapter: EventAdapter
    private lateinit var sessionManager: SessionManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(EventsViewModel::class.java)
        _binding = FragmentEventsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        setupRecyclerView()
        setupNewEventButton()
        observeViewModel()
        fetchEvents()
        setupBackNavigation()
    }

    /**
     * Sets up the new event button.
     * Only shows the button for superusers.
     */
    private fun setupNewEventButton() {
        FragmentUtils.setupSuperuserFab(
            this,
            binding.fabNewEvent,
            R.id.action_nav_events_to_nav_new_event
        )
    }

    /**
     * Sets up back navigation handling.
     */
    private fun setupBackNavigation() {
        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    // If we're at a top-level destination, let the system handle back
                    findNavController().navigateUp()
                }
            }
        )
    }

    /**
     * Sets up the RecyclerView for displaying event items with pagination.
     */
    private fun setupRecyclerView() {
        eventAdapter = EventAdapter { event ->
            // Navigate to event details
            val bundle = Bundle().apply {
                putString("shortUuid", event.shortUuid)
            }
            findNavController().navigate(R.id.action_nav_events_to_eventDetailsFragment, bundle)
        }

        binding.recyclerEvents.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = eventAdapter

            // Setup pagination using extension function
            setupPagination(
                onLoadMore = { loadMoreEvents() },
                hasMorePages = { viewModel.hasMorePages() },
                isLoadingMore = { viewModel.isLoadingMore.value ?: false }
            )
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.events.observe(viewLifecycleOwner) { events ->
            eventAdapter.submitList(events)

            // Show empty view if the list is empty
            if (events.isEmpty()) {
                binding.tvEmpty.visibility = View.VISIBLE
                binding.recyclerEvents.visibility = View.GONE
            } else {
                binding.tvEmpty.visibility = View.GONE
                binding.recyclerEvents.visibility = View.VISIBLE
            }
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE

            if (isLoading) {
                binding.tvError.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            }
        }

        viewModel.isLoadingMore.observe(viewLifecycleOwner) { isLoadingMore ->
            binding.progressBarPagination.visibility = if (isLoadingMore) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                binding.tvError.text = errorMessage
                binding.tvError.visibility = View.VISIBLE
                binding.recyclerEvents.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            } else {
                binding.tvError.visibility = View.GONE
            }
        }
    }

    /**
     * Fetches event items from the API.
     */
    private fun fetchEvents() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchEvents(token)
        } else {
            binding.tvError.text = getString(R.string.authentication_token_not_found)
            binding.tvError.visibility = View.VISIBLE
            binding.recyclerEvents.visibility = View.GONE
        }
    }

    /**
     * Loads more event items for pagination.
     */
    private fun loadMoreEvents() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.loadMoreEvents(token)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
