package ir.rahavardit.ariel.ui.faqs

import androidx.lifecycle.LiveData
import ir.rahavardit.ariel.data.model.FAQ
import ir.rahavardit.ariel.data.model.PaginatedResponse
import ir.rahavardit.ariel.data.repository.FAQRepository
import ir.rahavardit.ariel.ui.base.BasePaginatedViewModel

/**
 * ViewModel for the FAQs screen that handles FAQ data operations with pagination.
 */
class FAQsViewModel : BasePaginatedViewModel<FAQ>() {

    private val faqRepository = FAQRepository()

    val faqs: LiveData<List<FAQ>> = items

    override suspend fun fetchFirstPage(token: String, pageSize: Int): Result<PaginatedResponse<FAQ>> {
        return faqRepository.getFAQs(token, pageSize)
    }

    override suspend fun fetchFromUrl(token: String, url: String): Result<PaginatedResponse<FAQ>> {
        return faqRepository.getFAQsFromUrl(token, url)
    }

    /**
     * Fetches the first page of FAQ items.
     */
    fun fetchFAQs(token: String) = fetchItems(token)

    /**
     * Loads more FAQ items from the next page if available.
     */
    fun loadMoreFAQs(token: String) = loadMoreItems(token)
}
