package ir.rahavardit.ariel.ui.home

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.EventGroup
import ir.rahavardit.ariel.data.model.Ticket
import ir.rahavardit.ariel.data.model.TicketStatistics

import kotlinx.coroutines.launch

class HomeViewModel(private val sessionManager: SessionManager) : ViewModel() {

    private val apiService = RetrofitClient.getApiService()

    private val _ticketStatistics = MutableLiveData<List<TicketStatistics>>()
    val ticketStatistics: LiveData<List<TicketStatistics>> = _ticketStatistics

    private val _groups = MutableLiveData<List<EventGroup>>()
    val groups: LiveData<List<EventGroup>> = _groups

    private val _unvisitedEvents = MutableLiveData<Int>()
    val unvisitedEvents: LiveData<Int> = _unvisitedEvents

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading



    // Remove automatic loading from init - let Fragment control when to load

    /**
     * Loads homepage data including ticket statistics and groups from the API.
     */
    fun loadHomepageData() {
        // Log.d("HomeViewModel", "Loading homepage data...")

        val token = sessionManager.getAuthToken()
        if (token == null) {
            Log.e("HomeViewModel", "No auth token available")
            _ticketStatistics.value = emptyList()
            _groups.value = emptyList()
            _unvisitedEvents.value = 0
            return
        }

        viewModelScope.launch {
            _isLoading.value = true

            try {
                val response = apiService.getHomepageData("Token $token")
                // Log.d("HomeViewModel", "API response code: ${response.code()}")

                if (response.isSuccessful) {
                    val homepageData = response.body()
                    if (homepageData != null) {
                        val statistics = homepageData.getTicketStatisticsList()
                        // Log.d("HomeViewModel", "Loaded ${statistics.size} statistics")
                        _ticketStatistics.value = statistics

                        // Log.d("HomeViewModel", "Loaded ${homepageData.groups.size} groups")
                        _groups.value = homepageData.groups

                        // Log.d("HomeViewModel", "Unvisited events: ${homepageData.unvisitedEvents}")
                        _unvisitedEvents.value = homepageData.unvisitedEvents
                    } else {
                        Log.e("HomeViewModel", "Response body is null")
                        _ticketStatistics.value = emptyList()
                        _groups.value = emptyList()
                        _unvisitedEvents.value = 0
                    }
                } else {
                    Log.e("HomeViewModel", "API call failed: ${response.code()} - ${response.message()}")
                    _ticketStatistics.value = emptyList()
                    _groups.value = emptyList()
                    _unvisitedEvents.value = 0
                }
            } catch (e: Exception) {
                Log.e("HomeViewModel", "Error loading homepage data", e)
                _ticketStatistics.value = emptyList()
                _groups.value = emptyList()
                _unvisitedEvents.value = 0
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Refreshes homepage data.
     */
    fun refreshHomepageData() {
        loadHomepageData()
    }


}
