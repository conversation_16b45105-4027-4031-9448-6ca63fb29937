package ir.rahavardit.ariel.ui.knowledges

import androidx.lifecycle.LiveData
import ir.rahavardit.ariel.data.model.Knowledge
import ir.rahavardit.ariel.data.model.PaginatedResponse
import ir.rahavardit.ariel.data.repository.KnowledgeRepository
import ir.rahavardit.ariel.ui.base.BasePaginatedViewModel

/**
 * ViewModel for the knowledges screen that handles knowledge data operations with pagination.
 */
class KnowledgesViewModel : BasePaginatedViewModel<Knowledge>() {

    private val knowledgeRepository = KnowledgeRepository()

    val knowledges: LiveData<List<Knowledge>> = items

    override suspend fun fetchFirstPage(token: String, pageSize: Int): Result<PaginatedResponse<Knowledge>> {
        return knowledgeRepository.getKnowledges(token, pageSize)
    }

    override suspend fun fetchFromUrl(token: String, url: String): Result<PaginatedResponse<Knowledge>> {
        return knowledgeRepository.getKnowledgesFromUrl(token, url)
    }

    /**
     * Fetches the first page of knowledge items.
     */
    fun fetchKnowledges(token: String) = fetchItems(token)

    /**
     * Loads more knowledge items from the next page if available.
     */
    fun loadMoreKnowledges(token: String) = loadMoreItems(token)
}
