package ir.rahavardit.ariel.ui.ticketdetails

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.TicketResponse
import ir.rahavardit.ariel.databinding.ItemTicketResponseBinding
import ir.rahavardit.ariel.ui.base.BaseResponseAdapter
import ir.rahavardit.ariel.utils.HtmlRenderer

/**
 * Adapter for displaying ticket responses (children) in a RecyclerView.
 *
 * @property lifecycleScope The lifecycle scope for launching coroutines.
 */
class TicketResponseAdapter(private val lifecycleScope: LifecycleCoroutineScope) :
    ListAdapter<TicketResponse, TicketResponseAdapter.TicketResponseViewHolder>(TicketResponseDiffCallback()), BaseResponseAdapter()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TicketResponseViewHolder {
        val binding = ItemTicketResponseBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TicketResponseViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TicketResponseViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * ViewHolder for a ticket response item.
     */
    inner class TicketResponseViewHolder(private val binding: ItemTicketResponseBinding) :
        RecyclerView.ViewHolder(binding.root) {

        /**
         * Binds a ticket response to the ViewHolder.
         *
         * @param response The ticket response to bind.
         */
        fun bind(response: TicketResponse) {
            binding.apply {
                // Set basic information in a single row
                tvResponseId.text = response.shortUuid
                tvResponseAuthor.text = response.author.username
                tvResponseDate.text = response.createdJalali

                // Set message content with HTML support
                HtmlRenderer.applyToTextView(tvResponseMessage, response.message)

                // Reset signature view
                tvUserSignature.visibility = View.GONE
                tvUserSignature.text = ""

                // Highlight if the author is a superuser or limited admin
                if (response.author.isSuperuser || response.author.isLimitedAdmin) {
                    tvResponseAuthor.setTextColor(
                        ContextCompat.getColor(tvResponseAuthor.context, R.color.primary)
                    )

                    // Fetch and display signature for superuser
                    if (response.author.isSuperuser) {
                        fetchUserSignature(
                            lifecycleScope,
                            binding.root.context,
                            response.author.shortUuid,
                            tvUserSignature
                        )
                    }
                } else {
                    // Use secondary text color to match the date and short UUID
                    val typedArray = tvResponseAuthor.context.obtainStyledAttributes(intArrayOf(android.R.attr.textColorSecondary))
                    val textColor = typedArray.getColor(0, 0)
                    typedArray.recycle()
                    tvResponseAuthor.setTextColor(textColor)
                }

                // Handle file attachment for response
                setupFileAttachment(
                    response,
                    layoutResponseFile,
                    tvResponseFileName,
                    btnResponseDownloadFile,
                    binding.root.context
                )
            }
        }


    }

    /**
     * DiffUtil callback for comparing ticket responses.
     */
    private class TicketResponseDiffCallback : DiffUtil.ItemCallback<TicketResponse>() {
        override fun areItemsTheSame(oldItem: TicketResponse, newItem: TicketResponse): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: TicketResponse, newItem: TicketResponse): Boolean {
            return oldItem == newItem
        }
    }
}
