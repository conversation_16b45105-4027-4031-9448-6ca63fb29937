package ir.rahavardit.ariel.ui.events

import androidx.lifecycle.LiveData
import ir.rahavardit.ariel.data.model.Event
import ir.rahavardit.ariel.data.model.PaginatedResponse
import ir.rahavardit.ariel.data.repository.EventRepository
import ir.rahavardit.ariel.ui.base.BasePaginatedViewModel

/**
 * ViewModel for the events screen that handles event data operations with pagination.
 */
class EventsViewModel : BasePaginatedViewModel<Event>() {

    private val eventRepository = EventRepository()

    val events: LiveData<List<Event>> = items

    override suspend fun fetchFirstPage(token: String, pageSize: Int): Result<PaginatedResponse<Event>> {
        return eventRepository.getEvents(token, pageSize)
    }

    override suspend fun fetchFromUrl(token: String, url: String): Result<PaginatedResponse<Event>> {
        return eventRepository.getEventsFromUrl(token, url)
    }

    /**
     * Fetches the first page of events.
     */
    fun fetchEvents(token: String) = fetchItems(token)

    /**
     * Loads more events from the next page if available.
     */
    fun loadMoreEvents(token: String) = loadMoreItems(token)
}
