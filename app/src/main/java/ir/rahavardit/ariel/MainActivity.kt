package ir.rahavardit.ariel

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AlertDialog
import androidx.core.os.bundleOf
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.navigateUp
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.repository.AuthRepository
import ir.rahavardit.ariel.databinding.ActivityMainBinding
import ir.rahavardit.ariel.ui.login.LoginActivity
import ir.rahavardit.ariel.utils.BiometricAuthManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import kotlinx.coroutines.launch
import android.widget.Toast

class MainActivity : AppCompatActivity() {

    private lateinit var appBarConfiguration: AppBarConfiguration
    private lateinit var binding: ActivityMainBinding
    private lateinit var sessionManager: SessionManager
    private lateinit var navController: NavController
    private lateinit var biometricAuthManager: BiometricAuthManager
    private lateinit var authRepository: AuthRepository

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize session manager, biometric auth manager, and auth repository
        sessionManager = SessionManager(this)
        biometricAuthManager = BiometricAuthManager(this)
        authRepository = AuthRepository()

        // Check if user is logged in, if not redirect to login
        if (!sessionManager.isLoggedIn()) {
            redirectToLogin()
            return
        }

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.appBarMain.toolbar)

        // Hide the FAB in the main activity since we're using a FAB in the TicketsFragment
        binding.appBarMain.fab.visibility = View.GONE

        navController = findNavController(R.id.nav_host_fragment_content_main)

        // Set up navigation with back button for all screens except home
        // Only home screen won't show back button
        appBarConfiguration = AppBarConfiguration(
            setOf(R.id.nav_home)
        )
        setupActionBarWithNavController(navController, appBarConfiguration)

        // Set up back navigation
        setupBackNavigation()
    }



    /**
     * Sets up back navigation handling.
     */
    private fun setupBackNavigation() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // If we're at home, close the app
                // Otherwise, navigate up
                if (navController.currentDestination?.id == R.id.nav_home) {
                    finish()
                } else {
                    navController.navigateUp()
                }
            }
        })
    }



    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main, menu)

        // Set up action menu visibility based on user role
        setupActionMenuVisibility(menu)

        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_home -> {
                // Navigate to home
                navController.navigate(R.id.nav_home)
                true
            }
            R.id.action_search -> {
                // Show search dialog
                showSearchDialog()
                true
            }
            R.id.action_logout -> {
                // Show confirmation dialog before logging out
                showLogoutConfirmationDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * Shows a dialog for entering a search query.
     */
    private fun showSearchDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_search, null)
        val searchQueryEditText = dialogView.findViewById<EditText>(R.id.et_search_query)
        val searchButton = dialogView.findViewById<Button>(R.id.btn_search)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)
        val progressBar = dialogView.findViewById<ProgressBar>(R.id.progress_bar)

        val dialog = MaterialAlertDialogBuilder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        searchButton.setOnClickListener {
            val query = searchQueryEditText.text.toString().trim()
            if (query.isNotEmpty()) {
                // Navigate to search results fragment with the query
                val bundle = bundleOf("query" to query)
                navController.navigate(R.id.searchResultsFragment, bundle)
                dialog.dismiss()
            } else {
                // Show error if query is empty
                searchQueryEditText.error = getString(R.string.search_hint)
            }
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * Shows a confirmation dialog before logging out.
     */
    private fun showLogoutConfirmationDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_logout_confirm, null)
        val confirmButton = dialogView.findViewById<Button>(R.id.btn_confirm)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)

        val dialog = MaterialAlertDialogBuilder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        confirmButton.setOnClickListener {
            // User confirmed logout - perform logout with API call
            performLogout()
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * Performs logout by calling the API to invalidate the token and then clearing local session.
     */
    private fun performLogout() {
        val token = sessionManager.getAuthToken()

        if (token != null) {
            // Call logout API to invalidate token on backend
            lifecycleScope.launch {
                try {
                    val result = authRepository.logout(token)

                    if (result.isSuccess) {
                        // API call successful - clear session and redirect
                        sessionManager.clearSession()
                        redirectToLogin()
                    } else {
                        // JUMP_1
                        // API call failed but still clear local session
                        // This ensures user can logout even if network fails
                        Toast.makeText(
                            this@MainActivity,
                            getString(R.string.logout_completed_but_server_connection_error),
                            Toast.LENGTH_SHORT
                        ).show()
                        sessionManager.clearSession()
                        redirectToLogin()
                    }
                } catch (e: Exception) {
                    // JUMP_1
                    Toast.makeText(
                        this@MainActivity,
                        getString(R.string.logout_completed_but_server_connection_error),
                        Toast.LENGTH_SHORT
                    ).show()
                    sessionManager.clearSession()
                    redirectToLogin()
                }
            }
        } else {
            // No token found - just clear session and redirect
            sessionManager.clearSession()
            redirectToLogin()
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        val navController = findNavController(R.id.nav_host_fragment_content_main)
        return navController.navigateUp(appBarConfiguration) || super.onSupportNavigateUp()
    }

    /**
     * Redirects to the LoginActivity and finishes the current activity.
     */
    private fun redirectToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }

    /**
     * Sets up action menu item visibility based on user role.
     *
     * @param menu The action menu.
     */
    private fun setupActionMenuVisibility(menu: Menu) {
        val menuMenuItem = menu.findItem(R.id.action_menu)

        if (menuMenuItem?.hasSubMenu() == true) {
            val subMenu = menuMenuItem.subMenu

            // Search and logout are always visible (no role restrictions)
            val searchMenuItem = subMenu?.findItem(R.id.action_search)
            val logoutMenuItem = subMenu?.findItem(R.id.action_logout)
            searchMenuItem?.isVisible = true
            logoutMenuItem?.isVisible = true

            // The menu should always be visible since search and logout are always available
            menuMenuItem.isVisible = true
        }
    }

    /**
     * Performs biometric re-authentication for sensitive operations.
     * This can be called from fragments or other parts of the app when needed.
     *
     * @param onSuccess Callback to execute when authentication succeeds.
     * @param onError Callback to execute when authentication fails.
     */
    fun performBiometricReAuthentication(
        onSuccess: () -> Unit,
        onError: (String) -> Unit = { /* Default empty implementation */ }
    ) {
        val availability = biometricAuthManager.checkBiometricAvailability()

        if (availability != BiometricAuthManager.BiometricAvailability.AVAILABLE) {
            onError("Biometric authentication is not available")
            return
        }

        biometricAuthManager.authenticate(
            title = getString(R.string.biometric_title),
            subtitle = getString(R.string.biometric_subtitle),
            callback = object : BiometricAuthManager.BiometricAuthCallback {
                override fun onAuthenticationSucceeded() {
                    onSuccess()
                }

                override fun onAuthenticationError(errorMessage: String) {
                    onError(errorMessage)
                }
            }
        )
    }
}
