package ir.rahavardit.ariel.data.repository

import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.Knowledge
import ir.rahavardit.ariel.data.model.PaginatedResponse
import ir.rahavardit.ariel.utils.TokenUtils

/**
 * Repository class that handles knowledge-related operations.
 */
class KnowledgeRepository : BaseRepository() {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Retrieves the list of knowledge items with pagination.
     *
     * @param token The authentication token.
     * @param pageSize The number of knowledge items per page.
     * @return A Result containing either the paginated response or an Exception.
     */
    suspend fun getKnowledges(token: String, pageSize: Int = 15): Result<PaginatedResponse<Knowledge>> {
        return safeApiCall {
            apiService.getKnowledges(TokenUtils.formatToken(token), pageSize)
        }
    }

    /**
     * Retrieves knowledge items from a specific URL (for pagination).
     */
    suspend fun getKnowledgesFromUrl(token: String, url: String): Result<PaginatedResponse<Knowledge>> {
        return safeApiCall {
            apiService.getKnowledgesFromUrl(TokenUtils.formatToken(token), url)
        }
    }

    /**
     * Retrieves the details of a specific knowledge item by its short UUID.
     */
    suspend fun getKnowledgeDetails(token: String, shortUuid: String): Result<Knowledge> {
        return safeApiCall {
            apiService.getKnowledgeDetails(TokenUtils.formatToken(token), shortUuid)
        }
    }

    /**
     * Creates a new knowledge item.
     */
    suspend fun createKnowledge(token: String, title: String, body: String): Result<Knowledge> {
        return safeApiCall {
            val knowledgeRequest = mapOf(
                "title" to title,
                "body" to body
            )
            apiService.createKnowledge(TokenUtils.formatToken(token), knowledgeRequest)
        }
    }

    /**
     * Deletes a knowledge item by its short UUID.
     */
    suspend fun deleteKnowledge(token: String, shortUuid: String): Result<Unit> {
        return safeApiCallUnit {
            apiService.deleteKnowledge(TokenUtils.formatToken(token), shortUuid)
        }
    }

    /**
     * Updates a knowledge item by its short UUID.
     */
    suspend fun updateKnowledge(token: String, shortUuid: String, title: String, body: String): Result<Knowledge> {
        return safeApiCall {
            val knowledgeRequest = mapOf(
                "title" to title,
                "body" to body
            )
            apiService.updateKnowledge(TokenUtils.formatToken(token), shortUuid, knowledgeRequest)
        }
    }
}
