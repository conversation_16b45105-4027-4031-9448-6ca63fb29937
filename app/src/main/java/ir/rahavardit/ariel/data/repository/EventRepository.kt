package ir.rahavardit.ariel.data.repository

import android.content.Context
import android.net.Uri
import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.Event
import ir.rahavardit.ariel.data.model.PaginatedResponse
import ir.rahavardit.ariel.utils.FileUtils
import ir.rahavardit.ariel.utils.TokenUtils
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * Repository class that handles event-related operations.
 */
class EventRepository : BaseRepository() {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Retrieves the list of events with pagination.
     *
     * @param token The authentication token.
     * @param pageSize The number of events per page.
     * @return A Result containing either the paginated response or an Exception.
     */
    suspend fun getEvents(token: String, pageSize: Int = 15): Result<PaginatedResponse<Event>> {
        return safeApiCall {
            apiService.getEvents(TokenUtils.formatToken(token), pageSize)
        }
    }

    /**
     * Retrieves events from a specific URL (for pagination).
     *
     * @param token The authentication token.
     * @param url The full URL for the next page.
     * @return A Result containing either the paginated response or an Exception.
     */
    suspend fun getEventsFromUrl(token: String, url: String): Result<PaginatedResponse<Event>> {
        return safeApiCall {
            apiService.getEventsFromUrl(TokenUtils.formatToken(token), url)
        }
    }

    /**
     * Retrieves the details of a specific event by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the event.
     * @return A Result containing either the event details or an Exception.
     */
    suspend fun getEventDetails(token: String, shortUuid: String): Result<Event> {
        return safeApiCall {
            apiService.getEventDetails(TokenUtils.formatToken(token), shortUuid)
        }
    }

    /**
     * Deletes an event item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the event item to delete.
     * @return A Result indicating success or failure.
     */
    suspend fun deleteEvent(token: String, shortUuid: String): Result<Unit> {
        return safeApiCallUnit {
            apiService.deleteEvent(TokenUtils.formatToken(token), shortUuid)
        }
    }

    /**
     * Updates an event by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the event to update.
     * @param title The updated title of the event.
     * @param body The updated body content of the event.
     * @param groupId The updated ID of the group the event belongs to.
     * @return A Result containing either the updated event or an Exception.
     */
    suspend fun updateEvent(
        token: String,
        shortUuid: String,
        title: String,
        body: String,
        groupId: Int
    ): Result<Event> {
        return safeApiCall {
            val eventRequest = mapOf(
                "title" to title,
                "body" to body,
                "group_id" to groupId.toString()
            )
            apiService.updateEvent(TokenUtils.formatToken(token), shortUuid, eventRequest)
        }
    }

    /**
     * Updates an event with a file attachment by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the event to update.
     * @param title The updated title of the event.
     * @param body The updated body content of the event.
     * @param groupId The updated ID of the group the event belongs to.
     * @param fileUri The URI of the updated file to attach.
     * @param context The context to use for accessing the content resolver.
     * @return A Result containing either the updated event or an Exception.
     */
    suspend fun updateEventWithFile(
        token: String,
        shortUuid: String,
        title: String,
        body: String,
        groupId: Int,
        fileUri: Uri,
        context: Context
    ): Result<Event> {
        return safeApiCall {
            // Create request parts
            val titlePart = title.toRequestBody("text/plain".toMediaTypeOrNull())
            val bodyPart = body.toRequestBody("text/plain".toMediaTypeOrNull())
            val groupIdPart = groupId.toString().toRequestBody("text/plain".toMediaTypeOrNull())

            // Create file part using utility
            val filePart = FileUtils.createFilePart(fileUri, context)

            apiService.updateEventWithFile(
                TokenUtils.formatToken(token),
                shortUuid,
                titlePart,
                bodyPart,
                groupIdPart,
                filePart
            )
        }
    }


}
