package ir.rahavardit.ariel.data.repository

import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.FAQ
import ir.rahavardit.ariel.data.model.PaginatedResponse
import ir.rahavardit.ariel.utils.TokenUtils

/**
 * Repository class that handles FAQ-related operations.
 */
class FAQRepository : BaseRepository() {

    private val apiService = RetrofitClient.getApiService()

    suspend fun getFAQs(token: String, pageSize: Int = 15): Result<PaginatedResponse<FAQ>> {
        return safeApiCall {
            apiService.getFAQs(TokenUtils.formatToken(token), pageSize)
        }
    }

    suspend fun getFAQsFromUrl(token: String, url: String): Result<PaginatedResponse<FAQ>> {
        return safeApiCall {
            apiService.getFAQsFromUrl(TokenUtils.formatToken(token), url)
        }
    }

    suspend fun getFAQDetails(token: String, shortUuid: String): Result<FAQ> {
        return safeApiCall {
            apiService.getFAQDetails(TokenUtils.formatToken(token), shortUuid)
        }
    }

    suspend fun createFAQ(token: String, title: String, body: String): Result<FAQ> {
        return safeApiCall {
            val faqRequest = mapOf(
                "title" to title,
                "body" to body
            )
            apiService.createFAQ(TokenUtils.formatToken(token), faqRequest)
        }
    }

    suspend fun deleteFAQ(token: String, shortUuid: String): Result<Unit> {
        return safeApiCallUnit {
            apiService.deleteFAQ(TokenUtils.formatToken(token), shortUuid)
        }
    }

    suspend fun updateFAQ(token: String, shortUuid: String, title: String, body: String): Result<FAQ> {
        return safeApiCall {
            val faqRequest = mapOf(
                "title" to title,
                "body" to body
            )
            apiService.updateFAQ(TokenUtils.formatToken(token), shortUuid, faqRequest)
        }
    }
}
