package ir.rahavardit.ariel.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing the homepage data response from the API.
 * Contains both ticket statistics and groups in a single response.
 */
data class HomepageDataResponse(
    @SerializedName("tickets")
    val tickets: Map<String, TicketStatistics>,

    @SerializedName("groups")
    val groups: List<EventGroup>,

    @SerializedName("unvisited_events")
    val unvisitedEvents: Int = 0
) {
    /**
     * Converts the tickets map to a list of TicketStatistics for easier UI handling.
     */
    fun getTicketStatisticsList(): List<TicketStatistics> {
        return tickets.values.toList()
    }
}
