package ir.rahavardit.ariel.data.repository

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Base repository class with common functionality for API operations.
 */
abstract class BaseRepository {

    /**
     * Executes an API call with proper error handling and context switching.
     * 
     * @param apiCall The suspend function that makes the API call
     * @return Result containing either the response or an exception
     */
    protected suspend fun <T> safeApiCall(
        apiCall: suspend () -> retrofit2.Response<T>
    ): Result<T> {
        return withContext(Dispatchers.IO) {
            try {
                val response = apiCall()
                
                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("API call failed: ${response.code()} - ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Executes an API call that returns a simple success/failure result.
     * 
     * @param apiCall The suspend function that makes the API call
     * @return Result containing either Unit (success) or an exception
     */
    protected suspend fun safeApiCallUnit(
        apiCall: suspend () -> retrofit2.Response<Unit>
    ): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val response = apiCall()
                
                if (response.isSuccessful) {
                    Result.success(Unit)
                } else {
                    Result.failure(Exception("API call failed: ${response.code()} - ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
}
