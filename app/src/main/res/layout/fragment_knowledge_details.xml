<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="6dp"
    tools:context=".ui.knowledgedetails.KnowledgeDetailsFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Knowledge Item Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_knowledge"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                app:cardCornerRadius="8dp"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- short uuid and jalali date -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                            <TextView
                                android:id="@+id/tv_knowledge_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="?android:attr/textColorSecondary"
                                android:textSize="10sp"
                                tools:text="یک‌شنبه ۰۰:۱۸:۲۹ ۱۴۰۳/۰۳/۲۷" />

                            <TextView
                                android:id="@+id/tv_knowledge_id"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="?android:attr/textColorSecondary"
                                android:textSize="10sp"
                                tools:text="4588e326" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="16dp"
                        android:background="?android:attr/listDivider" />

                    <TextView
                        android:id="@+id/tv_knowledge_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textColor="?attr/colorOnSurface"
                        android:textStyle="bold"
                        tools:text="Knowledge Title" />

                    <TextView
                        android:id="@+id/tv_knowledge_body"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceBody1"
                        android:autoLink="web"
                        android:linksClickable="true"
                        tools:text="Knowledge body content goes here. This can be a long text with multiple paragraphs." />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Edit Button (Only visible for superusers) -->
            <Button
                android:id="@+id/btn_edit_knowledge"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                android:layout_marginTop="8dp"
                android:text="@string/edit"
                android:textColor="@color/edit_button_fg"
                android:backgroundTint="@color/edit_button_bg"
                app:strokeWidth="0dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <!-- Delete Button (Only visible for superusers) -->
            <Button
                android:id="@+id/btn_delete_knowledge"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                android:layout_marginTop="8dp"
                android:text="@string/delete"
                android:textColor="@android:color/white"
                android:backgroundTint="?attr/colorError"
                app:strokeWidth="0dp"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="4dp"
        android:gravity="center"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="?attr/colorError"
        android:visibility="gone"
        tools:text="Error loading knowledge details"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
