<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="6dp"
    tools:context=".ui.editevent.EditEventFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Edit Event Form Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_edit_event"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="4dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp"
                app:strokeColor="@color/card_border"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tv_edit_event_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/edit"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textColor="?attr/colorOnSurface"
                        android:textStyle="bold" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_event_title"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/event_title">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_event_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_event_group"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/event_group"
                        app:hintTextAppearance="@style/TextAppearance.MaterialComponents.Caption">

                        <AutoCompleteTextView
                            android:id="@+id/dropdown_group"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:textSize="12sp"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_event_body"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/event_body">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_event_body"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="top|start"
                            android:inputType="textMultiLine"
                            android:lines="10"
                            android:minLines="5" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <LinearLayout
                        android:id="@+id/layout_file_upload"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="vertical">

                            <Button
                                android:id="@+id/btn_select_file"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/select_file"
                                android:textColor="@color/select_file_button_fg"
                                android:backgroundTint="@color/select_file_button_bg"
                                app:strokeWidth="0dp"
                                app:icon="@drawable/ic_file_upload"
                                app:iconTint="@color/select_file_button_fg" />

                            <TextView
                                android:id="@+id/tv_selected_file"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginTop="8dp"
                                android:ellipsize="middle"
                                android:singleLine="true"
                                android:visibility="gone"
                                tools:text="selected_file.pdf"
                                tools:visibility="visible" />
                    </LinearLayout>

                    <Button
                        android:id="@+id/btn_submit"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/update"
                        android:textColor="@color/edit_button_fg"
                        android:backgroundTint="@color/edit_button_bg"
                        app:strokeWidth="0dp" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="4dp"
        android:gravity="center"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="?attr/colorError"
        android:visibility="gone"
        tools:text="Error message"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
