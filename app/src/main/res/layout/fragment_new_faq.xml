<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="6dp"
    tools:context=".ui.newfaq.NewFAQFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- New FAQ Form Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_new_faq"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp" >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tv_new_faq_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/new_faq"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textColor="?attr/colorOnSurface"
                        android:textStyle="bold" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_faq_title"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/faq_title">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_faq_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_faq_body"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/faq_body">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_faq_body"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="top|start"
                            android:inputType="textMultiLine"
                            android:lines="10"
                            android:minLines="5" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <Button
                        android:id="@+id/btn_submit"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/submit"
                        android:textColor="@color/submit_button_fg"
                        android:backgroundTint="@color/submit_button_bg"
                        app:strokeWidth="0dp" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="4dp"
        android:gravity="center"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="?attr/colorError"
        android:visibility="gone"
        tools:text="Error message"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
